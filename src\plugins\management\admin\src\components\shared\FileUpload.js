const React = require('react');
const { Upload, Button, message, Space } = require('antd');
const {
  UploadOutlined,
  FileOutlined,
  DeleteOutlined,
} = require('@ant-design/icons');
const styled = require('styled-components').default;

const FileUploadContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const FileListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const FileItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-family: 'Be Vietnam Pro', sans-serif;

  &:hover {
    background-color: #f0f0f0;
    border-color: #667eea;
  }
`;

const FileInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
`;

const FileName = styled.span`
  font-size: 14px;
  color: #262626;
  font-weight: 500;
`;

const FileSize = styled.span`
  font-size: 12px;
  color: #8c8c8c;
`;

const RemoveButton = styled(Button)`
  border: none;
  background: transparent;
  color: #ff4d4f;
  padding: 4px;
  height: auto;

  &:hover {
    background-color: #fff2f0;
    color: #ff4d4f;
  }
`;

const UploadButton = styled(Button)`
  height: 40px;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #fafafa;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;

  &:hover {
    border-color: #667eea;
    background-color: #f0f2ff;
    color: #667eea;
  }

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
  }
`;

const FileUpload = ({
  value = [],
  onChange,
  accept = '*',
  maxCount = 1,
  disabled = false,
  buttonText = 'Tải lên',
  beforeUpload,
  showFileList = true,
}) => {
  const handleUploadChange = (info) => {
    let newFileList = [...info.fileList];

    // Ensure each file has the correct status and uid
    newFileList = newFileList.map((file) => {
      if (!file.uid) {
        file.uid = `upload-${Date.now()}-${Math.random()}`;
      }
      if (!file.status) {
        file.status = 'done';
      }
      return file;
    });

    // Limit the number of files
    if (maxCount && newFileList.length > maxCount) {
      newFileList = newFileList.slice(-maxCount);
    }

    onChange?.(newFileList);
  };

  const handleRemove = (fileToRemove) => {
    const newFileList = value.filter((file) => file.uid !== fileToRemove.uid);
    onChange?.(newFileList);
  };

  const defaultBeforeUpload = (file) => {
    // Default validation can be customized
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('File phải nhỏ hơn 10MB!');
      return false;
    }

    return false; // Prevent auto upload
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const showUploadButton = !maxCount || value.length < maxCount;

  return React.createElement(
    FileUploadContainer,
    null,
    // Display uploaded files
    showFileList &&
      value.length > 0 &&
      React.createElement(
        FileListContainer,
        null,
        value.map((file) =>
          React.createElement(
            FileItem,
            { key: file.uid },
            React.createElement(
              FileInfo,
              null,
              React.createElement(FileOutlined, {
                style: { color: '#1890ff', fontSize: 16 },
              }),
              React.createElement(
                'div',
                null,
                React.createElement(FileName, null, file.name),
                file.size &&
                  React.createElement(
                    'div',
                    null,
                    React.createElement(
                      FileSize,
                      null,
                      formatFileSize(file.size)
                    )
                  )
              )
            ),
            !disabled &&
              React.createElement(RemoveButton, {
                type: 'text',
                size: 'small',
                icon: React.createElement(DeleteOutlined, null),
                onClick: () => handleRemove(file),
              })
          )
        )
      ),
    // Upload button
    showUploadButton &&
      !disabled &&
      React.createElement(
        Upload,
        {
          fileList: value,
          onChange: handleUploadChange,
          beforeUpload: beforeUpload || defaultBeforeUpload,
          accept: accept,
          maxCount: maxCount,
          showUploadList: false,
          disabled: disabled,
        },
        React.createElement(
          UploadButton,
          {
            icon: React.createElement(UploadOutlined, null),
            disabled: disabled,
            block: true,
          },
          buttonText
        )
      )
  );
};

module.exports = FileUpload;
